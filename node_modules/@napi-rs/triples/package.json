{"name": "@napi-rs/triples", "version": "1.2.0", "description": "Rust target triples objects", "keywords": ["Rust", "cross-compile", "napi", "n-api", "node-rs", "napi-rs"], "author": "LongYinan <<EMAIL>>", "homepage": "https://github.com/napi-rs/napi-rs/tree/main/triples#readme", "license": "MIT", "main": "./index.js", "types": "./index.d.ts", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "files": ["index.js", "index.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/napi-rs/napi-rs.git"}, "bugs": {"url": "https://github.com/napi-rs/napi-rs/issues"}, "gitHead": "184f1150ae5a5e14afb178daa52112b18b323002"}