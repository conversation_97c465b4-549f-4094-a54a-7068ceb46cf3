{"name": "@railway/cli", "version": "4.5.3", "description": "Develop and deploy code with zero configuration", "type": "module", "author": "<PERSON>", "license": "ISC", "homepage": "https://github.com/railwayapp/cli/blob/master/README.md", "repository": {"type": "git", "url": "https://github.com/railwayapp/cli.git"}, "engines": {"node": ">=16.0.0"}, "scripts": {"postinstall": "node ./npm-install/postinstall.js"}, "bin": {"railway": "bin/railway.js"}, "files": ["npm-install", "README.md"], "dependencies": {"@napi-rs/triples": "^1.1.0", "node-fetch": "^3.1.0", "tar": "^6.1.11"}}