import { NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  console.log(`🔍 Middleware: ${request.nextUrl.pathname}`);

  // For ALL API requests, provide default user context (development mode)
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const response = NextResponse.next();
    response.headers.set('x-user-id', 'dev-user-123');
    response.headers.set('x-user-email', '<EMAIL>');
    response.headers.set('x-organization-id', 'dev-org-123');
    console.log('🔧 Using development authentication bypass for:', request.nextUrl.pathname);
    return response;
  }

  // For non-API routes, just pass through
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*'
  ]
};
