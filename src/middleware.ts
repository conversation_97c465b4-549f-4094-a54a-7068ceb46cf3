import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from './lib/auth/portal-auth';

export async function middleware(request: NextRequest) {
  // Skip authentication for public routes
  const publicRoutes = ['/api/health', '/api/auth-config'];
  if (publicRoutes.some(route => request.nextUrl.pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Authenticate API requests
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Check if we're in development mode or Portal integration is not ready
    const isDevelopment = process.env.NODE_ENV === 'development';
    const portalIntegrationReady = process.env.PORTAL_INTEGRATION_READY === 'true';

    if (isDevelopment || !portalIntegrationReady) {
      // Fallback authentication for development/testing
      const response = NextResponse.next();
      response.headers.set('x-user-id', 'dev-user-123');
      response.headers.set('x-user-email', '<EMAIL>');
      response.headers.set('x-organization-id', 'dev-org-123');
      console.log('🔧 Using development authentication bypass');
      return response;
    }

    // Production Portal authentication
    const authContext = await authenticateRequest(request);

    if (!authContext) {
      return Response.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Add user context to headers for API routes
    const response = NextResponse.next();
    response.headers.set('x-user-id', authContext.user.id);
    response.headers.set('x-user-email', authContext.user.email);
    if (authContext.organizationId) {
      response.headers.set('x-organization-id', authContext.organizationId);
    }
    return response;
  }

  // For non-API routes, just pass through
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*'
  ]
};
