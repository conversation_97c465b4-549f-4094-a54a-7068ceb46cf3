import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from './lib/auth/portal-auth';

export async function middleware(request: NextRequest) {
  // Skip authentication for public routes
  const publicRoutes = ['/api/health', '/api/auth-config'];
  const isPublicRoute = publicRoutes.some(route => request.nextUrl.pathname === route);

  console.log(`🔍 Middleware: ${request.nextUrl.pathname}, isPublic: ${isPublicRoute}`);

  if (isPublicRoute) {
    console.log('✅ Public route - bypassing auth');
    return NextResponse.next();
  }

  // Authenticate API requests
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Check if Portal integration is ready
    const portalIntegrationReady = process.env.PORTAL_INTEGRATION_READY === 'true';
    console.log(`🔧 Portal integration ready: ${portalIntegrationReady}`);

    if (!portalIntegrationReady) {
      // Development/testing mode - provide default user context
      const response = NextResponse.next();
      response.headers.set('x-user-id', 'dev-user-123');
      response.headers.set('x-user-email', '<EMAIL>');
      response.headers.set('x-organization-id', 'dev-org-123');
      console.log('🔧 Using development authentication bypass');
      return response;
    }

    // Production Portal authentication
    const authContext = await authenticateRequest(request);

    if (!authContext) {
      return Response.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Add user context to headers for API routes
    const response = NextResponse.next();
    response.headers.set('x-user-id', authContext.user.id);
    response.headers.set('x-user-email', authContext.user.email);
    if (authContext.organizationId) {
      response.headers.set('x-organization-id', authContext.organizationId);
    }
    return response;
  }

  // For non-API routes, just pass through
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*'
  ]
};
