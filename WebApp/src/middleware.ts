import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Temporarily disabled auth for testing - always provide a default user ID
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const response = NextResponse.next();
    // Always set a default user ID for testing
    response.headers.set('x-user-id', 'test-user-123');
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*'
  ]
};
